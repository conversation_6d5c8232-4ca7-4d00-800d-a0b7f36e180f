好的，已根据您的要求，在原《焊接机器人视觉识别定位软件需求文档》的基础上，补充了关于 **多边形ROI** 和 **矩形ROI** 的具体需求，并对相关功能模块进行了整合与优化，确保内容清晰、完整。以下是更新后的完整需求文档：

---

# 焊接机器人视觉识别定位软件需求文档（修订版）

## 一、项目概述

本软件基于 Halcon 机器视觉库与 winform + .net framework 4.8 开发环境，实现焊接机器人的视觉识别与定位功能。通过视觉标定建立像素坐标与机器人物理坐标的映射关系，支持矩形与多边形ROI区域定义，创建可自定义定位点的产品模板，最终实现对产品焊接定位点的精准识别与坐标输出。现阶段以UI显示定位结果为核心目标，预留后期机器人通讯扩展能力。

---

## 二、核心功能需求

### （一）视觉标定功能

#### 1. 标定板参数定义
- 标定板包含3个实心圆形标记点，分别定义为 A、B、C，三点构成直角三角形，B为直角顶点。
- A与B的圆心距为120mm，B与C的圆心距为180mm。
- 标记点应具有高对比度，便于Halcon算法稳定检测。

#### 2. 图像采集与圆心检测
- 软件支持通过相机采集标定板图像，或导入已有图像进行处理。
- 使用Halcon的`find_circles`或`blob`分析系列算子自动检测A、B、C三点的像素坐标。
- 检测结果需在UI界面上以不同颜色标记（如A:红，B:绿，C:蓝），并实时显示其像素坐标（X_pix, Y_pix）。
- 支持用户手动微调圆心位置：可通过鼠标点击修正检测结果，修正后坐标实时更新。

#### 3. 机器人坐标输入
- 提供独立的标定坐标输入界面，支持手动输入A、B、C三点对应的机器人物理坐标（X、Y、Z轴）。
- 输入框需清晰标注点名（A/B/C）与坐标轴（X/Y/Z），支持浮点数输入。
- 数值校验机制：
  - X/Y坐标范围建议：±1000mm；
  - Z坐标非负；
  - 输入非法时弹出提示并阻止标定计算。

#### 4. 坐标映射模型建立
- 基于A、B、C三点的像素坐标与机器人物理坐标，调用Halcon标定函数（如`vector_to_hom_mat2d` + `hom_vector_to_world_plane`）建立2D仿射或透视变换模型。
- 模型建立后，计算反向投影误差（平均像素偏差），并在UI显示标定精度（如：“平均误差：0.35像素”）。
- 支持“标定模型验证”功能：
  - 用户可采集任意位置的标定板图像；
  - 自动检测圆心 → 通过映射模型计算物理坐标；
  - 显示计算值与实际测量值的对比及误差（ΔX, ΔY）；
  - 验证误差超过1mm时，提示“标定精度不足，请重新标定”。

---

### （二）ROI（感兴趣区域）管理功能

> **说明**：为提升图像处理效率与准确性，支持用户定义ROI区域，仅在指定区域内进行特征提取与匹配。

#### 1. ROI类型支持
- 支持两种ROI类型：
  - **矩形ROI**：可旋转矩形，适用于规则布局的产品区域。
  - **多边形ROI**：支持用户通过鼠标点击定义3个及以上顶点的闭合多边形，适用于不规则产品轮廓区域。

#### 2. ROI创建与编辑
- 在图像显示区域，提供“绘制ROI”工具按钮，用户可选择“矩形”或“多边形”模式。
- **矩形ROI**：
  - 支持拖拽绘制，支持旋转手柄调整角度；
  - 可手动输入中心点、长宽、旋转角度进行精确设置。
- **多边形ROI**：
  - 用户通过连续点击图像添加顶点，双击或点击“完成”闭合多边形；
  - 支持拖动顶点或边线进行调整；
  - 最少3个顶点，最多不限（建议≤20）。
- 所有ROI在图像上以半透明高亮边框显示，颜色可区分类型（如矩形：黄色；多边形：青色）。

#### 3. ROI应用与保存
- ROI可应用于以下功能模块：
  - 轮廓提取（仅在ROI内提取边缘）；
  - 模板匹配（仅在ROI内搜索匹配）；
  - 圆心检测（标定时可限定在ROI内搜索A/B/C点）。
- ROI配置可随模板一同保存，也可独立保存为`.roi`文件供其他模板复用。
- 支持ROI的加载、删除与启用/禁用切换。

---

### （三）模板创建功能

#### 1. 轮廓提取
- 支持导入产品图像或实时采集图像进行轮廓提取。
- 使用Halcon的`edges_sub_pix`或`contour_from_edges`等亚像素边缘检测算法提取产品轮廓。
- 轮廓提取结果在UI上以高亮线条显示（如红色）。
- 提供参数调节面板：
  - 边缘检测阈值（可调范围：10~100）；
  - 平滑系数（Sigma，可调范围：0.5~3.0）；
  - 参数调整后实时刷新轮廓显示。

#### 2. 定位点管理
- 用户可在提取的轮廓上通过鼠标点击添加定位点。
- 每个定位点记录以下信息：
  - 序号（按添加顺序自动编号）；
  - 像素坐标（X_pix, Y_pix）；
  - 所属轮廓的相对位置（建议保存为“轮廓弧长比例”或“相对偏移向量”，便于跨分辨率适配）。
- 支持定位点操作：
  - **删除**：选中定位点后点击“删除”按钮；
  - **调整**：支持拖动定位点或在属性面板中手动输入坐标修正；
  - **高亮显示**：鼠标悬停时显示序号与坐标。

#### 3. 产品高度设置
- 提供“产品高度”输入框，单位：mm，支持小数输入（如3.5）。
- 高度值作为预设值保存至模板中，用于Z轴坐标计算。
- 若后续通过测试获取精确高度，可更新并覆盖原值。

#### 4. 模板保存与加载
- 模板保存内容包括：
  - 模板名称；
  - ROI配置（类型、顶点坐标或参数）；
  - 轮廓数据（边缘点集或Halcon模板句柄序列化数据）；
  - 定位点信息（序号、像素坐标、相对轮廓坐标）；
  - 产品高度预设值；
  - 测试状态（已测试 / 未测试）。
- 保存格式：自定义`.weldtpl`二进制文件或XML格式，确保可读性与兼容性。
- 支持模板加载功能，加载后在UI还原所有图形元素与参数设置。

---

### （四）视觉识别定位功能

#### 1. 图像采集触发
- 支持两种触发方式：
  - **手动触发**：点击“采集图像”按钮；
  - **外部信号触发**：预留数字输入接口（如PLC信号），支持上升沿触发采集（后期扩展）。
- 采集图像实时显示在主界面，保留历史图像查看功能。

#### 2. 模板匹配参数设置
- 提供模板匹配参数调节区域：
  - **最大匹配偏移量**：
    - 支持分别设置像素单位（如±50px）和物理单位（如±5mm）；
    - 单位可切换，系统自动根据标定模型换算；
  - **最小匹配得分阈值**：0~100分，低于该值判定为匹配失败；
  - **搜索范围**：默认使用当前模板绑定的ROI，支持临时调整或禁用。

#### 3. 模板匹配
- 调用Halcon的`find_scaled_shape_model`或`find_shape_model`算法进行轮廓匹配。
- 匹配成功后，在图像上用绿色矩形框或轮廓线框选产品位置。
- 显示匹配得分（如“匹配得分：92.5”），颜色区分：
  - ≥阈值：绿色；
  - <阈值：红色，并提示“匹配失败，请检查产品位置或参数”。

#### 4. 坐标转换与输出
- 匹配成功后，根据模板中定位点的相对位置，结合匹配结果（位姿变换矩阵），计算各定位点的实际像素坐标。
- 调用标定阶段建立的映射模型，将像素坐标转换为机器人物理坐标（X_mm, Y_mm）。
- **Z轴物理坐标计算公式**：
  ```
  Z_物理 = Z_平台标定 + H_产品高度
  ```
  - `Z_平台标定`：标定时记录的平台Z轴坐标（手动输入）；
  - `H_产品高度`：模板中保存的产品高度（预设值或测试后更新的精确值）。
- 明确在UI显示产品高度来源状态（如：“高度来源：预设值” 或 “高度来源：实测值”）。

#### 5. 结果显示
- 在UI界面以表格形式按定位点序号列出以下信息：
  | 序号 | X_pix | Y_pix | X_mm | Y_mm | Z_mm |
  |------|-------|-------|------|------|------|
- 支持以下操作：
  - **截图保存**：保存当前图像与结果为PNG/JPG；
  - **导出Excel**：导出定位结果为`.xlsx`文件，包含时间戳、模板名称、各点坐标等信息。

#### 6. 模板测试与Z轴高度更新
- 提供“模板测试”功能界面：
  - 用户选择模板并启动测试；
  - 软件输出匹配到的XY坐标；
  - 操作人员控制机器人移动至该XY位置，Z轴停在“预设高度 + 10mm”处；
  - 缓慢下降Z轴直至接触产品表面，记录精确Z值；
  - 将该Z值减去`Z_平台标定`，得到精确的产品高度，更新模板中的`H_产品高度`。
- 若加载的模板未进行测试，UI需在显著位置显示红色警告文字：“⚠ 模板未测试，请进行Z轴高度测试”。

---

## 三、注意事项

1. **Z轴坐标计算逻辑**：
   - 必须严格按照公式 `Z_物理 = Z_平台标定 + H_产品高度` 计算；
   - `Z_平台标定`为标定过程中手动输入的平台Z值；
   - `H_产品高度`优先使用测试后的实测值，若未测试则使用预设值；
   - UI需清晰标识高度来源状态。

2. **机器人坐标输入方式**：
   - 当前阶段采用手动输入方式；
   - 需预留机器人通讯接口（如TCP/IP、Modbus TCP），用于后期自动读取机器人坐标；
   - 接口设计应支持主流品牌（KUKA、ABB、Fanuc）的坐标读取协议。

3. **定位结果输出方式**：
   - 当前版本无需向机器人发送数据，仅在UI显示结果；
   - 预留通讯输出接口，支持按格式（如“序号,X,Y,Z”）通过TCP或串口发送坐标序列。

4. **UI与用户体验建议**：
   - 增加“日志记录”区域，实时显示关键操作（如标定完成、模板保存、匹配成功/失败、误差值等），便于追溯与调试；
   - 模板管理界面应显示每个模板的“测试状态”（已测试 / 未测试），支持按状态筛选；
   - 所有图像操作支持缩放、平移、亮度对比度调节。

---

## 四、后续扩展规划

1. **机器人通讯模块**：
   - 集成TCP/IP或EtherNet/IP协议，实现与工业机器人的双向通信；
   - 支持自动获取机器人坐标用于标定，以及发送定位结果驱动焊接动作。

2. **自动化触发功能**：
   - 支持与PLC或传感器联动，通过光电开关、IO信号自动触发图像采集与识别流程；
   - 实现无人工干预的全自动焊接定位。

3. **误差分析功能**：
   - 添加统计模块，记录多次识别的坐标偏差；
   - 生成X/Y轴偏差分布图、趋势曲线，辅助优化标定与匹配参数；
   - 支持SPC过程控制图表输出。


---

## 五、补充事项

- 建议在模板创建时，定位点坐标优先保存“相对轮廓坐标”（如沿轮廓的弧长比例或相对于轮廓质心的偏移），以增强模板在不同成像条件下的鲁棒性。
- 所有Halcon算法模块需封装为独立类库，便于维护与参数调试。
- 软件应具备基本的异常处理机制，如图像采集失败、匹配超时、标定模型异常等，需有明确提示。

---

**（注：本文档部分内容由AI生成，已根据用户需求进行专业修订与补充）**

--- 

如需将此文档导出为 Word 或 PDF 格式，我也可以为您生成相应格式的内容。是否需要？